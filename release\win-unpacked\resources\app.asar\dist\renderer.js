/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./src/renderer/simple-renderer.ts":
/*!*****************************************!*\
  !*** ./src/renderer/simple-renderer.ts ***!
  \*****************************************/
/***/ (() => {

eval("\nclass PhantomBrowserUI {\n    securityStatus = null;\n    privacyStatus = null;\n    constructor() {\n        this.initializeUI();\n        this.setupEventListeners();\n        this.loadInitialData();\n    }\n    initializeUI() {\n        // Initialize the browser UI\n        this.updateSecurityIndicator();\n        this.updatePrivacyIndicators();\n        this.updateStats();\n    }\n    setupEventListeners() {\n        // Navigation buttons\n        document.getElementById('back-btn')?.addEventListener('click', () => this.goBack());\n        document.getElementById('forward-btn')?.addEventListener('click', () => this.goForward());\n        document.getElementById('refresh-btn')?.addEventListener('click', () => this.refresh());\n        document.getElementById('home-btn')?.addEventListener('click', () => this.goHome());\n        // URL bar\n        const urlBar = document.getElementById('url-bar');\n        urlBar?.addEventListener('keypress', (e) => {\n            if (e.key === 'Enter') {\n                this.navigateToUrl(urlBar.value);\n            }\n        });\n        // Start page search\n        const startSearch = document.getElementById('start-search');\n        const startSearchBtn = document.getElementById('start-search-btn');\n        startSearch?.addEventListener('keypress', (e) => {\n            if (e.key === 'Enter') {\n                this.performSearch(startSearch.value);\n            }\n        });\n        startSearchBtn?.addEventListener('click', () => {\n            this.performSearch(startSearch.value);\n        });\n        // Tab management\n        document.getElementById('new-tab-btn')?.addEventListener('click', () => this.createNewTab());\n        // Action cards\n        document.getElementById('security-card')?.addEventListener('click', () => {\n            this.showSecurityDashboard();\n        });\n        document.getElementById('privacy-card')?.addEventListener('click', () => {\n            this.showPrivacyCenter();\n        });\n        document.getElementById('toggle-tor')?.addEventListener('click', () => {\n            this.toggleTor();\n        });\n        // Toolbar buttons\n        document.getElementById('downloads-btn')?.addEventListener('click', () => {\n            this.showDownloads();\n        });\n        document.getElementById('bookmarks-btn')?.addEventListener('click', () => {\n            this.showBookmarks();\n        });\n        document.getElementById('settings-btn')?.addEventListener('click', () => {\n            this.showSettings();\n        });\n        document.getElementById('menu-btn')?.addEventListener('click', () => {\n            this.showMenu();\n        });\n        // Settings modal\n        document.querySelector('.modal-close')?.addEventListener('click', () => {\n            this.hideModal('settings-modal');\n        });\n        // Settings tabs\n        document.querySelectorAll('.settings-tab').forEach(tab => {\n            tab.addEventListener('click', (e) => {\n                const target = e.target;\n                const tabName = target.dataset.tab;\n                if (tabName) {\n                    this.showSettingsTab(tabName);\n                }\n            });\n        });\n        // Context menu\n        document.addEventListener('contextmenu', (e) => {\n            e.preventDefault();\n            this.showContextMenu(e.clientX, e.clientY);\n        });\n        document.addEventListener('click', () => {\n            this.hideContextMenu();\n        });\n    }\n    async loadInitialData() {\n        try {\n            if (window.phantomAPI) {\n                this.securityStatus = await window.phantomAPI.getSecurityStatus();\n                this.privacyStatus = await window.phantomAPI.getPrivacyStatus();\n                this.updateSecurityIndicator();\n                this.updatePrivacyIndicators();\n                this.updateStats();\n            }\n        }\n        catch (error) {\n            console.error('Failed to load initial data:', error);\n        }\n    }\n    navigateToUrl(url) {\n        if (!url.trim())\n            return;\n        // Add protocol if missing\n        if (!url.includes('://')) {\n            if (url.includes('.') && !url.includes(' ')) {\n                url = 'https://' + url;\n            }\n            else {\n                // Treat as search query\n                url = `https://duckduckgo.com/?q=${encodeURIComponent(url)}`;\n            }\n        }\n        if (window.phantomAPI) {\n            window.phantomAPI.createTab(url);\n        }\n        // Update URL bar\n        const urlBar = document.getElementById('url-bar');\n        if (urlBar) {\n            urlBar.value = url;\n        }\n        this.hideStartPage();\n    }\n    performSearch(query) {\n        if (!query.trim())\n            return;\n        const searchUrl = `https://duckduckgo.com/?q=${encodeURIComponent(query)}`;\n        this.navigateToUrl(searchUrl);\n    }\n    async createNewTab(url = 'about:blank') {\n        try {\n            if (window.phantomAPI) {\n                await window.phantomAPI.createTab(url);\n                this.hideStartPage();\n            }\n        }\n        catch (error) {\n            console.error('Failed to create new tab:', error);\n        }\n    }\n    goBack() {\n        window.history.back();\n    }\n    goForward() {\n        window.history.forward();\n    }\n    refresh() {\n        window.location.reload();\n    }\n    goHome() {\n        this.showStartPage();\n    }\n    toggleTor() {\n        const toggleBtn = document.getElementById('toggle-tor');\n        if (toggleBtn) {\n            const isEnabled = toggleBtn.textContent === 'Disable Tor';\n            toggleBtn.textContent = isEnabled ? 'Enable Tor' : 'Disable Tor';\n            // Update privacy status\n            if (this.privacyStatus) {\n                this.privacyStatus.torEnabled = !isEnabled;\n                this.updatePrivacyIndicators();\n            }\n        }\n    }\n    showSecurityDashboard() {\n        alert('Security Dashboard - Feature coming soon!');\n    }\n    showPrivacyCenter() {\n        alert('Privacy Center - Feature coming soon!');\n    }\n    showDownloads() {\n        alert('Downloads - Feature coming soon!');\n    }\n    showBookmarks() {\n        alert('Bookmarks - Feature coming soon!');\n    }\n    showSettings() {\n        this.showModal('settings-modal');\n        this.loadSettingsContent();\n    }\n    showMenu() {\n        alert('Menu - Feature coming soon!');\n    }\n    showModal(modalId) {\n        const modal = document.getElementById(modalId);\n        if (modal) {\n            modal.classList.remove('hidden');\n        }\n    }\n    hideModal(modalId) {\n        const modal = document.getElementById(modalId);\n        if (modal) {\n            modal.classList.add('hidden');\n        }\n    }\n    showContextMenu(x, y) {\n        const contextMenu = document.getElementById('context-menu');\n        if (contextMenu) {\n            contextMenu.style.left = `${x}px`;\n            contextMenu.style.top = `${y}px`;\n            contextMenu.classList.remove('hidden');\n        }\n    }\n    hideContextMenu() {\n        const contextMenu = document.getElementById('context-menu');\n        if (contextMenu) {\n            contextMenu.classList.add('hidden');\n        }\n    }\n    showStartPage() {\n        const startPage = document.getElementById('start-page');\n        if (startPage) {\n            startPage.style.display = 'flex';\n        }\n    }\n    hideStartPage() {\n        const startPage = document.getElementById('start-page');\n        if (startPage) {\n            startPage.style.display = 'none';\n        }\n    }\n    updateSecurityIndicator() {\n        const indicator = document.getElementById('security-indicator');\n        if (indicator && this.securityStatus) {\n            if (this.securityStatus.httpsEnforced && this.securityStatus.sandboxingEnabled) {\n                indicator.className = 'security-indicator secure';\n                indicator.title = 'Secure Connection';\n            }\n            else {\n                indicator.className = 'security-indicator warning';\n                indicator.title = 'Connection Security Warning';\n            }\n        }\n    }\n    updatePrivacyIndicators() {\n        const torIndicator = document.getElementById('tor-indicator');\n        const vpnIndicator = document.getElementById('vpn-indicator');\n        const adblockIndicator = document.getElementById('adblock-indicator');\n        if (this.privacyStatus) {\n            if (torIndicator) {\n                torIndicator.className = `privacy-indicator tor ${this.privacyStatus.torEnabled ? 'active' : ''}`;\n                torIndicator.title = `Tor: ${this.privacyStatus.torEnabled ? 'Enabled' : 'Disabled'}`;\n            }\n            if (vpnIndicator) {\n                vpnIndicator.className = `privacy-indicator vpn ${this.privacyStatus.vpnEnabled ? 'active' : ''}`;\n                vpnIndicator.title = `VPN: ${this.privacyStatus.vpnEnabled ? 'Enabled' : 'Disabled'}`;\n            }\n            if (adblockIndicator) {\n                adblockIndicator.className = `privacy-indicator adblock ${this.privacyStatus.adBlockingEnabled ? 'active' : ''}`;\n                adblockIndicator.title = `Ad Blocker: ${this.privacyStatus.adBlockingEnabled ? 'Enabled' : 'Disabled'}`;\n            }\n        }\n    }\n    updateStats() {\n        const blockedCount = document.getElementById('blocked-count');\n        const adsBlocked = document.getElementById('ads-blocked');\n        const dataSaved = document.getElementById('data-saved');\n        // Mock data for demonstration\n        if (blockedCount)\n            blockedCount.textContent = '1,247';\n        if (adsBlocked)\n            adsBlocked.textContent = '892';\n        if (dataSaved)\n            dataSaved.textContent = '15.3 MB';\n    }\n    loadSettingsContent() {\n        const settingsContent = document.getElementById('settings-content');\n        if (settingsContent) {\n            settingsContent.innerHTML = `\n        <div class=\"settings-section\">\n          <h3>General Settings</h3>\n          <div class=\"setting-item\">\n            <label>Default Search Engine</label>\n            <select>\n              <option value=\"duckduckgo\">DuckDuckGo</option>\n              <option value=\"startpage\">Startpage</option>\n              <option value=\"searx\">SearX</option>\n            </select>\n          </div>\n          <div class=\"setting-item\">\n            <label>\n              <input type=\"checkbox\" checked> Block third-party cookies\n            </label>\n          </div>\n          <div class=\"setting-item\">\n            <label>\n              <input type=\"checkbox\" checked> Enable HTTPS everywhere\n            </label>\n          </div>\n        </div>\n      `;\n        }\n    }\n    showSettingsTab(tabName) {\n        // Update active tab\n        document.querySelectorAll('.settings-tab').forEach(tab => {\n            tab.classList.remove('active');\n        });\n        const activeTab = document.querySelector(`[data-tab=\"${tabName}\"]`);\n        if (activeTab) {\n            activeTab.classList.add('active');\n        }\n        // Load tab content\n        this.loadSettingsTabContent(tabName);\n    }\n    loadSettingsTabContent(tabName) {\n        const settingsContent = document.getElementById('settings-content');\n        if (!settingsContent)\n            return;\n        switch (tabName) {\n            case 'general':\n                this.loadSettingsContent();\n                break;\n            case 'security':\n                settingsContent.innerHTML = '<div>Security settings content...</div>';\n                break;\n            case 'privacy':\n                settingsContent.innerHTML = '<div>Privacy settings content...</div>';\n                break;\n            case 'advanced':\n                settingsContent.innerHTML = '<div>Advanced settings content...</div>';\n                break;\n        }\n    }\n}\n// Initialize the browser UI when DOM is loaded\ndocument.addEventListener('DOMContentLoaded', () => {\n    new PhantomBrowserUI();\n});\n\n\n//# sourceURL=webpack://phantom-browser/./src/renderer/simple-renderer.ts?");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	var __webpack_exports__ = {};
/******/ 	__webpack_modules__["./src/renderer/simple-renderer.ts"]();
/******/ 	
/******/ })()
;