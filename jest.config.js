/** @type {import('jest').Config} */
module.exports = {
  // Test environment configuration
  testEnvironment: 'node',
  
  // TypeScript support
  preset: 'ts-jest',
  
  // Test file patterns
  testMatch: [
    '<rootDir>/tests/**/*.test.ts',
    '<rootDir>/tests/**/*.spec.ts',
    '<rootDir>/src/**/__tests__/**/*.ts',
    '<rootDir>/src/**/*.test.ts'
  ],
  
  // File extensions to consider
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
  
  // Transform configuration
  transform: {
    '^.+\\.tsx?$': ['ts-jest', {
      tsconfig: {
        compilerOptions: {
          module: 'commonjs',
          target: 'es2020',
          lib: ['es2020', 'dom'],
          moduleResolution: 'node',
          allowSyntheticDefaultImports: true,
          esModuleInterop: true,
          skipLibCheck: true,
          strict: true,
          declaration: false,
          declarationMap: false,
          sourceMap: true
        }
      }
    }]
  },
  
  // Module name mapping for path aliases
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@security/(.*)$': '<rootDir>/src/security/$1',
    '^@privacy/(.*)$': '<rootDir>/src/privacy/$1',
    '^@ui/(.*)$': '<rootDir>/src/ui/$1',
    '^@main/(.*)$': '<rootDir>/src/main/$1',
    '^@renderer/(.*)$': '<rootDir>/src/renderer/$1',
    '^@preload/(.*)$': '<rootDir>/src/preload/$1',
    '^@tests/(.*)$': '<rootDir>/tests/$1'
  },
  
  // Setup files
  setupFilesAfterEnv: [
    '<rootDir>/tests/setup/jest.setup.ts'
  ],
  
  // Mock configuration
  clearMocks: true,
  resetMocks: true,
  restoreMocks: true,
  
  // Coverage configuration
  collectCoverage: true,
  coverageDirectory: 'coverage',
  coverageReporters: [
    'text',
    'text-summary',
    'html',
    'lcov',
    'json',
    'clover'
  ],
  
  // Coverage collection patterns
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/**/__tests__/**',
    '!src/**/*.test.ts',
    '!src/**/*.spec.ts',
    '!src/main/simple-main.ts',
    '!src/renderer/simple-renderer.ts',
    '!src/preload/simple-preload.ts'
  ],
  
  // Coverage thresholds
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 75,
      lines: 80,
      statements: 80
    },
    './src/security/': {
      branches: 80,
      functions: 85,
      lines: 90,
      statements: 90
    },
    './src/privacy/': {
      branches: 80,
      functions: 85,
      lines: 90,
      statements: 90
    }
  },
  
  // Test timeout (moved to individual test configurations)
  // testTimeout: 30000,
  
  // Ignore patterns
  testPathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/dist/',
    '<rootDir>/release/',
    '<rootDir>/build/'
  ],
  
  // Module paths to ignore for transformation
  transformIgnorePatterns: [
    'node_modules/(?!(electron|@electron)/)'
  ],
  
  // Global test configuration
  globals: {
    'ts-jest': {
      isolatedModules: true,
      useESM: false
    }
  },
  
  // Test results processor
  verbose: true,
  
  // Error handling
  errorOnDeprecated: true,
  
  // Test projects for different environments
  projects: [
    {
      displayName: 'Main Process Tests',
      testMatch: [
        '<rootDir>/tests/main/**/*.test.ts',
        '<rootDir>/tests/security/**/*.test.ts',
        '<rootDir>/tests/privacy/**/*.test.ts'
      ],
      testEnvironment: 'node',
      setupFilesAfterEnv: ['<rootDir>/tests/setup/main.setup.ts']
    },
    {
      displayName: 'Renderer Process Tests',
      testMatch: [
        '<rootDir>/tests/renderer/**/*.test.ts',
        '<rootDir>/tests/ui/**/*.test.ts'
      ],
      testEnvironment: 'jsdom',
      setupFilesAfterEnv: ['<rootDir>/tests/setup/renderer.setup.ts']
    },
    {
      displayName: 'Integration Tests',
      testMatch: [
        '<rootDir>/tests/integration/**/*.test.ts'
      ],
      testEnvironment: 'node',
      setupFilesAfterEnv: ['<rootDir>/tests/setup/integration.setup.ts'],
      testTimeout: 60000
    }
  ],
  
  // Reporter configuration
  reporters: [
    'default',
    ['jest-html-reporters', {
      publicPath: './coverage/html-report',
      filename: 'test-report.html',
      expand: true,
      hideIcon: false,
      pageTitle: 'Phantom Browser Test Report'
    }],
    ['jest-junit', {
      outputDirectory: './coverage',
      outputName: 'junit.xml',
      suiteName: 'Phantom Browser Tests'
    }]
  ],
  
  // Watch mode configuration
  watchPathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/dist/',
    '<rootDir>/release/',
    '<rootDir>/coverage/'
  ]
};
