/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./src/preload/simple-preload.ts":
/*!***************************************!*\
  !*** ./src/preload/simple-preload.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst electron_1 = __webpack_require__(/*! electron */ \"electron\");\n// Implement the secure API\nconst phantomAPI = {\n    // Tab management\n    createTab: (url) => electron_1.ipcRenderer.invoke('create-tab', url),\n    // Security\n    getSecurityStatus: () => electron_1.ipcRenderer.invoke('get-security-status'),\n    // Privacy\n    getPrivacyStatus: () => electron_1.ipcRenderer.invoke('get-privacy-status')\n};\n// Security: Only expose the API we want to expose\nelectron_1.contextBridge.exposeInMainWorld('phantomAPI', phantomAPI);\n// Security: Block access to Node.js APIs\ndelete window.require;\ndelete window.exports;\ndelete window.module;\n// Security: Override dangerous globals\nwindow.eval = undefined;\nwindow.Function = undefined;\n// Privacy: Override fingerprinting APIs\nconst originalGetContext = HTMLCanvasElement.prototype.getContext;\nHTMLCanvasElement.prototype.getContext = function (contextType, ...args) {\n    if (contextType === '2d' || contextType === 'webgl' || contextType === 'webgl2') {\n        // Return a proxy that modifies fingerprinting methods\n        const context = originalGetContext.call(this, contextType, ...args);\n        if (context) {\n            return createCanvasProxy(context);\n        }\n    }\n    return originalGetContext.call(this, contextType, ...args);\n};\nfunction createCanvasProxy(context) {\n    return new Proxy(context, {\n        get(target, prop) {\n            if (prop === 'getImageData') {\n                return function (...args) {\n                    const result = target[prop].apply(target, args);\n                    // Add noise to prevent fingerprinting\n                    if (result && result.data) {\n                        for (let i = 0; i < result.data.length; i += 4) {\n                            result.data[i] += Math.random() * 0.1 - 0.05; // Red\n                            result.data[i + 1] += Math.random() * 0.1 - 0.05; // Green\n                            result.data[i + 2] += Math.random() * 0.1 - 0.05; // Blue\n                        }\n                    }\n                    return result;\n                };\n            }\n            if (prop === 'toDataURL') {\n                return function (...args) {\n                    // Add slight noise to canvas data\n                    const imageData = target.getImageData(0, 0, target.canvas.width, target.canvas.height);\n                    for (let i = 0; i < imageData.data.length; i += 4) {\n                        imageData.data[i] += Math.random() * 0.1 - 0.05;\n                        imageData.data[i + 1] += Math.random() * 0.1 - 0.05;\n                        imageData.data[i + 2] += Math.random() * 0.1 - 0.05;\n                    }\n                    target.putImageData(imageData, 0, 0);\n                    return target[prop].apply(target, args);\n                };\n            }\n            return target[prop];\n        }\n    });\n}\n// Privacy: Override WebGL fingerprinting\nconst originalGetParameter = WebGLRenderingContext.prototype.getParameter;\nWebGLRenderingContext.prototype.getParameter = function (parameter) {\n    // Spoof common fingerprinting parameters\n    switch (parameter) {\n        case this.VENDOR:\n            return 'Intel Inc.';\n        case this.RENDERER:\n            return 'Intel Iris OpenGL Engine';\n        case this.VERSION:\n            return 'WebGL 1.0 (OpenGL ES 2.0 Chromium)';\n        case this.SHADING_LANGUAGE_VERSION:\n            return 'WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)';\n        default:\n            return originalGetParameter.call(this, parameter);\n    }\n};\n// Privacy: Override audio fingerprinting\nconst AudioContext = window.AudioContext || window.webkitAudioContext;\nif (AudioContext) {\n    const originalCreateAnalyser = AudioContext.prototype.createAnalyser;\n    AudioContext.prototype.createAnalyser = function () {\n        const analyser = originalCreateAnalyser.call(this);\n        const originalGetFloatFrequencyData = analyser.getFloatFrequencyData;\n        analyser.getFloatFrequencyData = function (array) {\n            originalGetFloatFrequencyData.call(this, array);\n            // Add noise to prevent audio fingerprinting\n            for (let i = 0; i < array.length; i++) {\n                array[i] += Math.random() * 0.001 - 0.0005;\n            }\n        };\n        return analyser;\n    };\n}\n// Privacy: Override font enumeration\nconst originalFonts = document.fonts;\nif (originalFonts) {\n    Object.defineProperty(document, 'fonts', {\n        get: function () {\n            // Return a limited set of fonts to prevent fingerprinting\n            return {\n                check: () => false,\n                load: () => Promise.resolve([]),\n                ready: Promise.resolve(),\n                addEventListener: () => { },\n                removeEventListener: () => { },\n                values: () => [],\n                entries: () => [],\n                keys: () => [],\n                forEach: () => { },\n                has: () => false,\n                add: () => { },\n                delete: () => false,\n                clear: () => { },\n                size: 0\n            };\n        }\n    });\n}\n// Security: CSP enforcement\nconst meta = document.createElement('meta');\nmeta.httpEquiv = 'Content-Security-Policy';\nmeta.content = \"default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https:; font-src 'self' data:; object-src 'none'; media-src 'self' https:; frame-src 'none';\";\ndocument.head.appendChild(meta);\nconsole.log('Phantom Browser preload script loaded with security and privacy protections');\n\n\n//# sourceURL=webpack://phantom-browser/./src/preload/simple-preload.ts?");

/***/ }),

/***/ "electron":
/*!***************************!*\
  !*** external "electron" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("electron");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("./src/preload/simple-preload.ts");
/******/ 	
/******/ })()
;