import { BrowserWindow, <PERSON><PERSON><PERSON><PERSON>ie<PERSON> } from 'electron';
interface Tab {
    id: string;
    view: BrowserView;
    url: string;
    title: string;
    isPrivate: boolean;
    isActive: boolean;
}
export declare class BrowserWindowManager {
    private tabs;
    private activeTabId;
    private mainWindow;
    constructor();
    setMainWindow(window: BrowserWindow): void;
    createTab(url?: string, isPrivate?: boolean): string;
    closeTab(tabId: string): boolean;
    setActiveTab(tabId: string): boolean;
    navigateTab(tabId: string, url: string): boolean;
    createPrivateWindow(): BrowserWindow;
    getTabs(): Tab[];
    getActiveTab(): Tab | null;
    private configureTabSecurity;
    private setupViewEventHandlers;
    private setupEventHandlers;
    private isUrlSafe;
}
export {};
//# sourceMappingURL=BrowserWindowManager.d.ts.map