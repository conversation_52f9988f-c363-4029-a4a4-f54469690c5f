interface PrivacyStatus {
    torEnabled: boolean;
    vpnEnabled: boolean;
    adBlockingEnabled: boolean;
    trackingProtectionEnabled: boolean;
    dnsOverHttpsEnabled: boolean;
    cookieBlockingEnabled: boolean;
    lastPrivacyCheck: Date;
}
export declare class PrivacyManager {
    private secureStore;
    private adBlocker;
    private torManager;
    private torEnabled;
    private vpnEnabled;
    private dohEnabled;
    private dnsProviders;
    constructor();
    setupSecureDNS(): void;
    setupAdBlocking(ses: Electron.Session): void;
    toggleTor(enabled: boolean): Promise<boolean>;
    toggleVPN(enabled: boolean, config?: any): Promise<boolean>;
    clearBrowsingData(): Promise<void>;
    getPrivacyStatus(): PrivacyStatus;
    updateAdBlockLists(): Promise<void>;
    addCustomBlockRule(rule: string): void;
    removeCustomBlockRule(rule: string): void;
    private configureTorProxy;
    private removeTorProxy;
    private configureVPNProxy;
    private removeVPNProxy;
    private isTrackingCookie;
    private setupPrivacyDefaults;
    private getPrivacyFocusedUserAgent;
    private loadPrivacySettings;
}
export {};
//# sourceMappingURL=PrivacyManager.d.ts.map