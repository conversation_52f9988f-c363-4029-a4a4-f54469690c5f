declare global {
    interface Window {
        phantomAPI: {
            createTab: (url: string) => Promise<string>;
            getSecurityStatus: () => Promise<any>;
            getPrivacyStatus: () => Promise<any>;
        };
    }
}
declare class PhantomBrowserUI {
    private securityStatus;
    private privacyStatus;
    constructor();
    private initializeUI;
    private setupEventListeners;
    private loadInitialData;
    private navigateToUrl;
    private performSearch;
    private createNewTab;
    private goBack;
    private goForward;
    private refresh;
    private goHome;
    private toggleTor;
    private showSecurityDashboard;
    private showPrivacyCenter;
    private showDownloads;
    private showBookmarks;
    private showSettings;
    private showMenu;
    private showModal;
    private hideModal;
    private showContextMenu;
    private hideContextMenu;
    private showStartPage;
    private hideStartPage;
    private updateSecurityIndicator;
    private updatePrivacyIndicators;
    private updateStats;
    private loadSettingsContent;
    private showSettingsTab;
    private loadSettingsTabContent;
}
//# sourceMappingURL=simple-renderer.d.ts.map