directories:
  output: release
  buildResources: build
appId: com.phantom.browser
productName: Phantom Browser
files:
  - filter:
      - dist/**/*
      - assets/**/*
win:
  target:
    - target: nsis
      arch:
        - x64
    - target: portable
      arch:
        - x64
  publisherName: Phantom Browser
  requestedExecutionLevel: asInvoker
  sign: false
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
portable:
  artifactName: PhantomBrowser-${version}-portable.exe
publish: null
compression: normal
npmRebuild: false
electronVersion: 28.3.3
