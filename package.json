{"name": "phantom-browser", "version": "1.0.0", "description": "A highly secure, privacy-focused web browser with maximum protection against data leaks and tracking", "main": "dist/main.js", "scripts": {"build": "webpack --mode production", "build:dev": "webpack --mode development", "dev": "webpack --mode development --watch", "start": "electron dist/main.js", "start:dev": "concurrently \"npm run dev\" \"wait-on dist/main.js && electron dist/main.js\"", "package": "electron-builder", "package:all": "electron-builder --mac --win --linux", "test": "jest", "lint": "eslint src/**/*.ts", "type-check": "tsc --noEmit"}, "keywords": ["browser", "privacy", "security", "electron", "tor", "vpn", "tracking-protection"], "author": "Phantom Browser Team", "license": "MIT", "devDependencies": {"@types/node": "^20.10.0", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "concurrently": "^8.2.2", "copy-webpack-plugin": "^11.0.0", "css-loader": "^6.8.1", "electron": "^28.0.0", "electron-builder": "^24.8.0", "eslint": "^8.54.0", "jest": "^29.7.0", "style-loader": "^3.3.3", "ts-loader": "^9.5.1", "typescript": "^5.3.0", "wait-on": "^7.2.0", "webpack": "^5.89.0", "webpack-cli": "^5.1.4"}, "dependencies": {"crypto-js": "^4.2.0", "electron-store": "^8.1.0", "node-forge": "^1.3.1", "socks-proxy-agent": "^8.0.2", "tough-cookie": "^4.1.3", "uuid": "^9.0.1"}, "build": {"appId": "com.phantom.browser", "productName": "<PERSON> Browser", "directories": {"output": "release"}, "files": ["dist/**/*", "assets/**/*"], "mac": {"category": "public.app-category.productivity", "hardenedRuntime": true, "entitlements": "build/entitlements.mac.plist", "entitlementsInherit": "build/entitlements.mac.plist"}, "win": {"target": "nsis", "publisherName": "<PERSON> Browser"}, "linux": {"target": "AppImage", "category": "Network"}, "publish": null}, "engines": {"node": ">=18.0.0"}}