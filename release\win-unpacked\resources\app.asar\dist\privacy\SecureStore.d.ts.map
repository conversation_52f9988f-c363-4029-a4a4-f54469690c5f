{"version": 3, "file": "SecureStore.d.ts", "sourceRoot": "", "sources": ["../../src/privacy/SecureStore.ts"], "names": [], "mappings": "AAWA,qBAAa,KAAK;IAChB,OAAO,CAAC,QAAQ,CAAS;IACzB,OAAO,CAAC,aAAa,CAAS;IAC9B,OAAO,CAAC,IAAI,CAA+B;gBAE/B,QAAQ,EAAE,MAAM;IAOrB,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,IAAI;IAKlC,GAAG,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG;IAIrB,GAAG,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO;IAIzB,MAAM,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO;IAQ5B,KAAK,IAAI,IAAI;IAKb,IAAI,IAAI,gBAAgB,CAAC,MAAM,CAAC;IAIhC,MAAM,IAAI,gBAAgB,CAAC,GAAG,CAAC;IAI/B,OAAO,IAAI,gBAAgB,CAAC,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAI1C,IAAI,IAAI,MAAM;IAIrB,OAAO,CAAC,mBAAmB;IAU3B,OAAO,CAAC,YAAY;IA0BpB,OAAO,CAAC,OAAO;IAiBf,OAAO,CAAC,OAAO;IAcf,OAAO,CAAC,QAAQ;IAiBhB,OAAO,CAAC,QAAQ;IAyBT,MAAM,CAAC,UAAU,EAAE,MAAM,GAAG,OAAO;IAanC,OAAO,CAAC,UAAU,EAAE,MAAM,GAAG,OAAO;IAcpC,YAAY,IAAI,IAAI;IAuBpB,YAAY,IAAI,MAAM;IAItB,YAAY,IAAI,MAAM;IAWtB,WAAW,IAAI,OAAO;IAItB,iBAAiB,IAAI,GAAG;CAUhC"}