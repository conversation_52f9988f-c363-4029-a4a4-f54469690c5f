{"name": "electron-packager", "version": "17.1.2", "description": "Customize and package your Electron app with OS-specific bundles (.app, .exe, etc.) via JS or CLI", "main": "src/index.js", "types": "src/index.d.ts", "bin": {"electron-packager": "bin/electron-packager.js"}, "repository": {"type": "git", "url": "git+https://github.com/electron/electron-packager.git"}, "author": "<PERSON>", "license": "BSD-2-<PERSON><PERSON>", "bugs": {"url": "https://github.com/electron/electron-packager/issues"}, "homepage": "https://github.com/electron/electron-packager", "files": ["collaborators.md", "bin", "docs/*.md", "NEWS.md", "src", "usage.txt"], "dependencies": {"@electron/asar": "^3.2.1", "@electron/get": "^2.0.0", "@electron/notarize": "^1.2.3", "@electron/osx-sign": "^1.0.5", "@electron/universal": "^1.3.2", "cross-spawn-windows-exe": "^1.2.0", "debug": "^4.0.1", "extract-zip": "^2.0.0", "filenamify": "^4.1.0", "fs-extra": "^11.1.0", "galactus": "^1.0.0", "get-package-info": "^1.0.0", "junk": "^3.1.0", "parse-author": "^2.0.0", "plist": "^3.0.0", "rcedit": "^3.0.1", "resolve": "^1.1.6", "semver": "^7.1.3", "yargs-parser": "^21.1.1"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^4.0.1", "@typescript-eslint/parser": "^4.0.1", "ava": "^3.10.1", "buffer-equal": "^1.0.0", "codecov": "^3.0.0", "eslint": "^7.12.1", "eslint-config-standard": "^16.0.0", "eslint-plugin-ava": "^13.2.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.0.0", "eslint-plugin-promise": "^5.1.0", "nyc": "^15.0.0", "pkg-up": "^3.0.1", "sinon": "^15.0.1", "tsd": "^0.14.0", "typedoc": "^0.19.0", "typescript": "^4.0.2"}, "engines": {"node": ">= 14.17.5"}, "scripts": {"ava": "ava test/index.js", "codecov": "nyc report --reporter=text-lcov > coverage.lcov && codecov", "coverage": "nyc ava test/index.js", "docs:build": "node test/ci/build_docs.js", "lint": "npm run lint:js && npm run lint:ts", "lint:js": "eslint .", "lint:ts": "eslint --config .eslintrc.typescript.js --ext .ts .", "test": "npm run lint && npm run tsd && npm run coverage", "tsd": "tsd"}, "directories": {"test": "test"}, "keywords": ["electron", "packager", "bundler", "app"], "ava": {"timeout": "60s"}, "eslintConfig": {"extends": ["eslint:recommended", "plugin:ava/recommended", "plugin:import/errors", "plugin:import/warnings", "plugin:promise/recommended", "standard"], "parserOptions": {"sourceType": "script"}, "plugins": ["ava"], "rules": {"ava/no-import-test-files": 0, "ava/no-ignored-test-files": 0, "indent": ["error", 2, {"CallExpression": {"arguments": "first"}, "SwitchCase": 1}], "no-console": 0, "strict": "error"}}, "eslintIgnore": ["typedoc/**/*", "**/test/fixtures/**/node_modules/**", "**/test/work/*"], "tsd": {"directory": "test"}, "funding": {"url": "https://github.com/electron/electron-packager?sponsor=1"}, "resolutions": {"got": "11.8.5"}}