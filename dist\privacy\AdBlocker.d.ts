export declare class AdBlocker {
    private blockLists;
    private customRules;
    private store;
    private enabled;
    private blockedCount;
    private defaultBlockLists;
    private trackingDomains;
    private adDomains;
    constructor();
    shouldBlockRequest(url: string, resourceType?: string): boolean;
    addCustomRule(rule: string): void;
    removeCustomRule(rule: string): void;
    getCustomRules(): string[];
    updateBlockLists(): Promise<void>;
    getBlockedCount(): number;
    resetBlockedCount(): void;
    isEnabled(): boolean;
    setEnabled(enabled: boolean): void;
    getBlockListStats(): any;
    private isTrackingDomain;
    private isAdDomain;
    private matchesCustomRules;
    private matchesBlockLists;
    private shouldBlockResourceType;
    private matchesRule;
    private parseBlockListContent;
    private initializeDefaultRules;
    private loadConfiguration;
    private saveConfiguration;
}
//# sourceMappingURL=AdBlocker.d.ts.map