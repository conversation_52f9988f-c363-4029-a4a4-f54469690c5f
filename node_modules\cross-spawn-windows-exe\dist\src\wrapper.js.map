{"version": 3, "file": "wrapper.js", "sourceRoot": "", "sources": ["../../src/wrapper.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA,qEAIqC;AACrC,uCAAyB;AACzB,oDAA2B;AAC3B,2CAA6B;AAC7B,kDAA0B;AAE1B,SAAgB,wBAAwB;IACtC,OAAO,OAAO,CAAC,QAAQ,KAAK,OAAO,IAAI,gBAAK,CAAC;AAC/C,CAAC;AAFD,4DAEC;AAED;;GAEG;AACH,MAAa,YAAa,SAAQ,KAAK;IACrC;;;OAGG;IACH,YAAY,cAAsB,EAAE,mBAA4B;QAC9D,MAAM,OAAO,GAAG,oBAAoB,cAAc,6BAChD,mBAAmB,CAAC,CAAC,CAAC,GAAG,GAAG,mBAAmB,CAAC,CAAC,CAAC,EACpD,EAAE,CAAC;QACH,KAAK,CAAC,OAAO,CAAC,CAAC;IACjB,CAAC;CACF;AAXD,oCAWC;AA4BD;;;GAGG;AACI,KAAK,UAAU,oBAAoB,CACxC,cAAsB;IAEtB,IAAI,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE;QACnC,OAAO,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;KACtC;SAAM;QACL,IAAI;YACF,MAAM,eAAK,CAAC,cAAc,CAAC,CAAC;YAC5B,OAAO,IAAI,CAAC;SACb;QAAC,WAAM;YACN,OAAO,KAAK,CAAC;SACd;KACF;AACH,CAAC;AAbD,oDAaC;AAED;;;GAGG;AACI,KAAK,UAAU,YAAY,CAChC,GAAW,EACX,IAAqB,EACrB,OAA8B;IAE9B,OAAO,aAAP,OAAO,cAAP,OAAO,IAAP,OAAO,GAAK,EAA0B,EAAC;IAEvC,MAAM,EAAE,cAAc,EAAE,mBAAmB,EAAE,GAAG,iBAAiB,EAAE,GAAG,OAAO,CAAC;IAC9E,IAAI,cAAc,EAAE;QAClB,IAAI,CAAC,CAAC,MAAM,oBAAoB,CAAC,cAAc,CAAC,CAAC,EAAE;YACjD,MAAM,IAAI,YAAY,CAAC,cAAc,EAAE,mBAAmB,CAAC,CAAC;SAC7D;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAEpD,OAAO,2BAAK,CAAC,cAAc,EAAE,aAAa,EAAE,iBAAiB,CAAC,CAAC;KAChE;IAED,OAAO,2BAAK,CAAC,GAAG,EAAE,IAAI,EAAE,iBAAiB,CAAC,CAAC;AAC7C,CAAC;AAnBD,oCAmBC;AAED;;;GAGG;AACI,KAAK,UAAU,wBAAwB,CAC5C,eAAyC,EACzC,GAAW,EACX,IAAqB,EACrB,OAA8B;IAE9B,IAAI,UAAU,GAAG,OAAO,CAAC;IACzB,IAAI,CAAC,wBAAwB,EAAE,EAAE;QAC/B,MAAM,cAAc,GAAW,eAAe,CAAC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,cAAc,CAAC,CAAC;QACxE,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,EAAE,cAAc,EAAE,CAAC,CAAC,CAAC,EAAE,cAAc,EAAE,CAAC;KAC5E;IACD,OAAO,YAAY,CAAC,GAAG,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;AAC7C,CAAC;AAZD,4DAYC"}