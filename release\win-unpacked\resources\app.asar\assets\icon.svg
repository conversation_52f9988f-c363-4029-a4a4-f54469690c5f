<?xml version="1.0" encoding="UTF-8"?>
<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2196F3;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background circle -->
  <circle cx="128" cy="128" r="120" fill="url(#grad1)" filter="url(#shadow)"/>
  
  <!-- Shield shape -->
  <path d="M128 40 L80 60 L80 120 C80 160 100 190 128 200 C156 190 176 160 176 120 L176 60 Z" 
        fill="#ffffff" opacity="0.9"/>
  
  <!-- Inner shield -->
  <path d="M128 60 L95 75 L95 125 C95 155 110 175 128 180 C146 175 161 155 161 125 L161 75 Z" 
        fill="url(#grad1)"/>
  
  <!-- Lock icon -->
  <rect x="115" y="110" width="26" height="20" rx="2" fill="#ffffff"/>
  <path d="M120 110 L120 105 C120 100 123 97 128 97 C133 97 136 100 136 105 L136 110" 
        stroke="#ffffff" stroke-width="3" fill="none"/>
  
  <!-- Eye symbol for privacy -->
  <ellipse cx="128" cy="90" rx="15" ry="8" fill="#ffffff" opacity="0.8"/>
  <circle cx="128" cy="90" r="4" fill="url(#grad1)"/>
  
  <!-- Phantom text -->
  <text x="128" y="220" font-family="Arial, sans-serif" font-size="16" font-weight="bold" 
        text-anchor="middle" fill="#333333">PHANTOM</text>
</svg>
