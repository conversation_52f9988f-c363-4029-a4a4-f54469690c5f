declare global {
    interface Window {
        phantomAPI: any;
    }
}
declare class PhantomBrowserUI {
    private tabs;
    private activeTabId;
    private securityStatus;
    private privacyStatus;
    constructor();
    private initializeUI;
    private setupEventListeners;
    private loadInitialData;
    private navigateToUrl;
    private performSearch;
    private createNewTab;
    private closeTab;
    private goBack;
    private goForward;
    private refresh;
    private goHome;
    private toggleTor;
    private showSecurityDashboard;
    private showPrivacyCenter;
    private showDownloads;
    private showBookmarks;
    private showSettings;
    private showMenu;
    private showModal;
    private hideModal;
    private showContextMenu;
    private hideContextMenu;
    private showStartPage;
    private hideStartPage;
    private updateSecurityIndicator;
    private updatePrivacyIndicators;
    private updateStats;
    private loadSettingsContent;
    private showSettingsTab;
    private loadSettingsTabContent;
    private onTabCreated;
    private onTabClosed;
    private onTabUpdated;
    private showSecurityAlert;
    private showPrivacyAlert;
}
//# sourceMappingURL=renderer.d.ts.map