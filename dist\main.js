/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./src/main/simple-main.ts":
/*!*********************************!*\
  !*** ./src/main/simple-main.ts ***!
  \*********************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || (function () {\n    var ownKeys = function(o) {\n        ownKeys = Object.getOwnPropertyNames || function (o) {\n            var ar = [];\n            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n            return ar;\n        };\n        return ownKeys(o);\n    };\n    return function (mod) {\n        if (mod && mod.__esModule) return mod;\n        var result = {};\n        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n        __setModuleDefault(result, mod);\n        return result;\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst electron_1 = __webpack_require__(/*! electron */ \"electron\");\nconst path = __importStar(__webpack_require__(/*! path */ \"path\"));\nclass PhantomBrowser {\n    mainWindow = null;\n    constructor() {\n        this.initializeApp();\n    }\n    initializeApp() {\n        // Security: Disable node integration in renderer processes by default\n        electron_1.app.commandLine.appendSwitch('--disable-web-security', 'false');\n        electron_1.app.commandLine.appendSwitch('--site-per-process');\n        electron_1.app.commandLine.appendSwitch('--disable-features', 'VizDisplayCompositor');\n        // Privacy: Disable telemetry and data collection\n        electron_1.app.commandLine.appendSwitch('--disable-background-timer-throttling');\n        electron_1.app.commandLine.appendSwitch('--disable-backgrounding-occluded-windows');\n        electron_1.app.commandLine.appendSwitch('--disable-renderer-backgrounding');\n        this.setupEventHandlers();\n    }\n    setupEventHandlers() {\n        electron_1.app.whenReady().then(() => {\n            this.setupSecurity();\n            this.createMainWindow();\n            this.setupMenu();\n            this.setupIpcHandlers();\n        });\n        electron_1.app.on('window-all-closed', () => {\n            if (process.platform !== 'darwin') {\n                electron_1.app.quit();\n            }\n        });\n        electron_1.app.on('activate', () => {\n            if (electron_1.BrowserWindow.getAllWindows().length === 0) {\n                this.createMainWindow();\n            }\n        });\n        // Security: Prevent new window creation from renderer\n        electron_1.app.on('web-contents-created', (event, contents) => {\n            contents.on('will-navigate', (event, navigationUrl) => {\n                const parsedUrl = new URL(navigationUrl);\n                // Block dangerous protocols\n                if (!['http:', 'https:', 'file:'].includes(parsedUrl.protocol)) {\n                    event.preventDefault();\n                }\n            });\n        });\n    }\n    setupSecurity() {\n        // Configure session security\n        const ses = electron_1.session.defaultSession;\n        // Security headers\n        ses.webRequest.onHeadersReceived((details, callback) => {\n            const responseHeaders = details.responseHeaders || {};\n            // Add security headers\n            responseHeaders['X-Frame-Options'] = ['DENY'];\n            responseHeaders['X-Content-Type-Options'] = ['nosniff'];\n            responseHeaders['X-XSS-Protection'] = ['1; mode=block'];\n            responseHeaders['Referrer-Policy'] = ['strict-origin-when-cross-origin'];\n            responseHeaders['Permissions-Policy'] = ['geolocation=(), microphone=(), camera=()'];\n            callback({ responseHeaders });\n        });\n        // Clear data on startup for privacy\n        ses.clearStorageData({\n            storages: ['cookies', 'filesystem', 'indexdb', 'localstorage', 'shadercache', 'websql', 'serviceworkers', 'cachestorage']\n        });\n        // Configure privacy settings\n        ses.setPermissionRequestHandler((webContents, permission, callback) => {\n            // Deny all permissions by default for privacy\n            callback(false);\n        });\n        // Block tracking and ads\n        ses.webRequest.onBeforeRequest((details, callback) => {\n            const url = new URL(details.url);\n            // Block known tracking domains\n            const trackingDomains = [\n                'google-analytics.com',\n                'googletagmanager.com',\n                'facebook.com/tr',\n                'doubleclick.net',\n                'googlesyndication.com'\n            ];\n            if (trackingDomains.some(domain => url.hostname.includes(domain))) {\n                callback({ cancel: true });\n                return;\n            }\n            callback({ cancel: false });\n        });\n    }\n    createMainWindow() {\n        this.mainWindow = new electron_1.BrowserWindow({\n            width: 1200,\n            height: 800,\n            minWidth: 800,\n            minHeight: 600,\n            show: false,\n            icon: path.join(__dirname, '../../assets/icon.png'),\n            webPreferences: {\n                nodeIntegration: false,\n                contextIsolation: true,\n                preload: path.join(__dirname, 'preload.js'),\n                sandbox: true,\n                webSecurity: true,\n                allowRunningInsecureContent: false,\n                experimentalFeatures: false\n            }\n        });\n        this.mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));\n        this.mainWindow.once('ready-to-show', () => {\n            this.mainWindow?.show();\n            if (true) {\n                this.mainWindow?.webContents.openDevTools();\n            }\n        });\n        this.mainWindow.on('closed', () => {\n            this.mainWindow = null;\n        });\n        // Security: Prevent navigation to external sites in main window\n        this.mainWindow.webContents.on('will-navigate', (event, url) => {\n            if (!url.startsWith('file://')) {\n                event.preventDefault();\n            }\n        });\n    }\n    setupMenu() {\n        const template = [\n            {\n                label: 'File',\n                submenu: [\n                    {\n                        label: 'New Tab',\n                        accelerator: 'CmdOrCtrl+T',\n                        click: () => this.createNewTab()\n                    },\n                    {\n                        label: 'New Private Window',\n                        accelerator: 'CmdOrCtrl+Shift+N',\n                        click: () => this.createPrivateWindow()\n                    },\n                    { type: 'separator' },\n                    {\n                        label: 'Exit',\n                        accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',\n                        click: () => electron_1.app.quit()\n                    }\n                ]\n            },\n            {\n                label: 'Security',\n                submenu: [\n                    {\n                        label: 'Security Dashboard',\n                        click: () => this.showSecurityDashboard()\n                    },\n                    {\n                        label: 'Certificate Manager',\n                        click: () => this.showCertificateManager()\n                    }\n                ]\n            },\n            {\n                label: 'Privacy',\n                submenu: [\n                    {\n                        label: 'Privacy Settings',\n                        click: () => this.showPrivacySettings()\n                    },\n                    {\n                        label: 'Clear Browsing Data',\n                        accelerator: 'CmdOrCtrl+Shift+Delete',\n                        click: () => this.clearBrowsingData()\n                    }\n                ]\n            }\n        ];\n        const menu = electron_1.Menu.buildFromTemplate(template);\n        electron_1.Menu.setApplicationMenu(menu);\n    }\n    setupIpcHandlers() {\n        electron_1.ipcMain.handle('create-tab', (event, url) => {\n            return this.createNewTab(url);\n        });\n        electron_1.ipcMain.handle('get-security-status', () => {\n            return {\n                httpsEnforced: true,\n                certificatePinningEnabled: false,\n                fingerprintingProtectionEnabled: true,\n                memoryIsolationEnabled: true,\n                sandboxingEnabled: true,\n                lastSecurityCheck: new Date()\n            };\n        });\n        electron_1.ipcMain.handle('get-privacy-status', () => {\n            return {\n                torEnabled: false,\n                vpnEnabled: false,\n                adBlockingEnabled: true,\n                trackingProtectionEnabled: true,\n                dnsOverHttpsEnabled: true,\n                cookieBlockingEnabled: true,\n                lastPrivacyCheck: new Date()\n            };\n        });\n    }\n    createNewTab(url = 'about:blank') {\n        // For now, just navigate the main window\n        if (this.mainWindow && url !== 'about:blank') {\n            this.mainWindow.loadURL(url);\n        }\n        return 'main-tab';\n    }\n    createPrivateWindow() {\n        const privateWindow = new electron_1.BrowserWindow({\n            width: 1200,\n            height: 800,\n            show: false,\n            webPreferences: {\n                nodeIntegration: false,\n                contextIsolation: true,\n                sandbox: true,\n                partition: 'private-session'\n            }\n        });\n        privateWindow.loadFile(path.join(__dirname, '../renderer/index.html'));\n        privateWindow.once('ready-to-show', () => {\n            privateWindow.show();\n        });\n    }\n    showSecurityDashboard() {\n        console.log('Security Dashboard');\n    }\n    showCertificateManager() {\n        console.log('Certificate Manager');\n    }\n    showPrivacySettings() {\n        console.log('Privacy Settings');\n    }\n    clearBrowsingData() {\n        const ses = electron_1.session.defaultSession;\n        ses.clearStorageData();\n        console.log('Browsing data cleared');\n    }\n}\n// Initialize the browser\nnew PhantomBrowser();\n\n\n//# sourceURL=webpack://phantom-browser/./src/main/simple-main.ts?");

/***/ }),

/***/ "electron":
/*!***************************!*\
  !*** external "electron" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("electron");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module is referenced by other modules so it can't be inlined
/******/ 	var __webpack_exports__ = __webpack_require__("./src/main/simple-main.ts");
/******/ 	
/******/ })()
;