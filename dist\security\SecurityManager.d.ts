interface SecurityStatus {
    httpsEnforced: boolean;
    certificatePinningEnabled: boolean;
    fingerprintingProtectionEnabled: boolean;
    memoryIsolationEnabled: boolean;
    sandboxingEnabled: boolean;
    lastSecurityCheck: Date;
}
export declare class SecurityManager {
    private certificatePins;
    private secureStore;
    private fingerprintingProtection;
    constructor();
    verifyCertificate(request: Electron.CertificateVerifyProcProcRequest, callback: (verificationResult: number) => void): void;
    addCertificatePin(hostname: string, fingerprint: string, algorithm?: string): void;
    removeCertificatePin(hostname: string, fingerprint?: string): void;
    setupFingerprintingProtection(): void;
    setupMemoryProtection(): void;
    getSecurityStatus(): SecurityStatus;
    toggleFingerprintingProtection(enabled: boolean): void;
    performSecurityAudit(): Promise<any>;
    private calculateCertificateFingerprint;
    private performAdditionalSecurityChecks;
    private getCertificateChainLength;
    private isCanvasFingerprintingAttempt;
    private getStandardizedUserAgent;
    private generateSecurityRecommendations;
    private loadCertificatePins;
    private saveCertificatePins;
}
export {};
//# sourceMappingURL=SecurityManager.d.ts.map