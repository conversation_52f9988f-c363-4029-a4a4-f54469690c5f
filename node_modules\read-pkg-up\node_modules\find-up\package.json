{"name": "find-up", "version": "2.1.0", "description": "Find a file by walking up parent directories", "license": "MIT", "repository": "sindresorhus/find-up", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "package", "resolve", "parent", "parents", "folder", "directory", "dir", "walk", "walking", "path"], "dependencies": {"locate-path": "^2.0.0"}, "devDependencies": {"ava": "*", "tempfile": "^1.1.1", "xo": "*"}, "xo": {"esnext": true}}