interface UpdateInfo {
    version: string;
    releaseDate: Date;
    downloadUrl: string;
    signature: string;
    checksum: string;
    releaseNotes: string;
    critical: boolean;
}
interface UpdateConfig {
    checkInterval: number;
    autoDownload: boolean;
    autoInstall: boolean;
    updateChannel: 'stable' | 'beta' | 'dev';
    lastCheck: Date | null;
}
export declare class UpdateManager {
    private store;
    private config;
    private updateCheckTimer;
    private publicKey;
    constructor();
    checkForUpdates(manual?: boolean): Promise<UpdateInfo | null>;
    downloadUpdate(updateInfo: UpdateInfo): Promise<boolean>;
    installUpdate(updatePath: string, updateInfo: UpdateInfo): Promise<void>;
    setUpdateConfig(config: Partial<UpdateConfig>): void;
    getUpdateConfig(): UpdateConfig;
    private fetchUpdateInfo;
    private verifyUpdateSignature;
    private verifyChecksum;
    private isNewerVersion;
    private promptUserForUpdate;
    private promptUserForInstallation;
    private performInstallation;
    private scheduleInstallationOnRestart;
    private scheduleUpdateCheck;
    private getUpdateUrl;
    private getUpdateFilePath;
    private getPublicKey;
    private loadConfig;
    private saveConfig;
}
export {};
//# sourceMappingURL=UpdateManager.d.ts.map