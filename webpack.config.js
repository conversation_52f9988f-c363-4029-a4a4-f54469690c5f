const path = require('path');

module.exports = [
  // Main process configuration
  {
    target: 'electron-main',
    entry: './src/main/simple-main.ts',
    output: {
      path: path.resolve(__dirname, 'dist'),
      filename: 'main.js'
    },
    module: {
      rules: [
        {
          test: /\.ts$/,
          use: 'ts-loader',
          exclude: /node_modules/
        }
      ]
    },
    resolve: {
      extensions: ['.ts', '.js']
    },
    node: {
      __dirname: false,
      __filename: false
    },
    externals: {
      'electron': 'commonjs electron'
    }
  },
  // Renderer process configuration
  {
    target: 'electron-renderer',
    entry: './src/renderer/simple-renderer.ts',
    output: {
      path: path.resolve(__dirname, 'dist'),
      filename: 'renderer.js'
    },
    module: {
      rules: [
        {
          test: /\.ts$/,
          use: 'ts-loader',
          exclude: /node_modules/
        },
        {
          test: /\.css$/,
          use: ['style-loader', 'css-loader']
        }
      ]
    },
    resolve: {
      extensions: ['.ts', '.js', '.css']
    },
    externals: {
      'electron': 'commonjs electron'
    }
  },
  // Preload script configuration
  {
    target: 'electron-preload',
    entry: './src/preload/simple-preload.ts',
    output: {
      path: path.resolve(__dirname, 'dist'),
      filename: 'preload.js'
    },
    module: {
      rules: [
        {
          test: /\.ts$/,
          use: 'ts-loader',
          exclude: /node_modules/
        }
      ]
    },
    resolve: {
      extensions: ['.ts', '.js']
    },
    externals: {
      'electron': 'commonjs electron'
    }
  }
];
