// Declare the phantom API interface
declare global {
  interface Window {
    phantomAPI: {
      createTab: (url: string) => Promise<string>;
      getSecurityStatus: () => Promise<any>;
      getPrivacyStatus: () => Promise<any>;
    };
  }
}

class PhantomBrowserUI {
  private securityStatus: any = null;
  private privacyStatus: any = null;

  constructor() {
    this.initializeUI();
    this.setupEventListeners();
    this.loadInitialData();
  }

  private initializeUI(): void {
    // Initialize the browser UI
    this.updateSecurityIndicator();
    this.updatePrivacyIndicators();
    this.updateStats();
  }

  private setupEventListeners(): void {
    // Navigation buttons
    document.getElementById('back-btn')?.addEventListener('click', () => this.goBack());
    document.getElementById('forward-btn')?.addEventListener('click', () => this.goForward());
    document.getElementById('refresh-btn')?.addEventListener('click', () => this.refresh());
    document.getElementById('home-btn')?.addEventListener('click', () => this.goHome());

    // URL bar
    const urlBar = document.getElementById('url-bar') as HTMLInputElement;
    urlBar?.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        this.navigateToUrl(urlBar.value);
      }
    });

    // Start page search
    const startSearch = document.getElementById('start-search') as HTMLInputElement;
    const startSearchBtn = document.getElementById('start-search-btn');
    
    startSearch?.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        this.performSearch(startSearch.value);
      }
    });
    
    startSearchBtn?.addEventListener('click', () => {
      this.performSearch(startSearch.value);
    });

    // Tab management
    document.getElementById('new-tab-btn')?.addEventListener('click', () => this.createNewTab());

    // Action cards
    document.getElementById('security-card')?.addEventListener('click', () => {
      this.showSecurityDashboard();
    });
    
    document.getElementById('privacy-card')?.addEventListener('click', () => {
      this.showPrivacyCenter();
    });
    
    document.getElementById('toggle-tor')?.addEventListener('click', () => {
      this.toggleTor();
    });

    // Toolbar buttons
    document.getElementById('downloads-btn')?.addEventListener('click', () => {
      this.showDownloads();
    });
    
    document.getElementById('bookmarks-btn')?.addEventListener('click', () => {
      this.showBookmarks();
    });
    
    document.getElementById('settings-btn')?.addEventListener('click', () => {
      this.showSettings();
    });
    
    document.getElementById('menu-btn')?.addEventListener('click', () => {
      this.showMenu();
    });

    // Settings modal
    document.querySelector('.modal-close')?.addEventListener('click', () => {
      this.hideModal('settings-modal');
    });

    // Settings tabs
    document.querySelectorAll('.settings-tab').forEach(tab => {
      tab.addEventListener('click', (e) => {
        const target = e.target as HTMLElement;
        const tabName = target.dataset.tab;
        if (tabName) {
          this.showSettingsTab(tabName);
        }
      });
    });

    // Context menu
    document.addEventListener('contextmenu', (e) => {
      e.preventDefault();
      this.showContextMenu(e.clientX, e.clientY);
    });

    document.addEventListener('click', () => {
      this.hideContextMenu();
    });
  }

  private async loadInitialData(): Promise<void> {
    try {
      if (window.phantomAPI) {
        this.securityStatus = await window.phantomAPI.getSecurityStatus();
        this.privacyStatus = await window.phantomAPI.getPrivacyStatus();
        
        this.updateSecurityIndicator();
        this.updatePrivacyIndicators();
        this.updateStats();
      }
    } catch (error) {
      console.error('Failed to load initial data:', error);
    }
  }

  private navigateToUrl(url: string): void {
    if (!url.trim()) return;

    // Add protocol if missing
    if (!url.includes('://')) {
      if (url.includes('.') && !url.includes(' ')) {
        url = 'https://' + url;
      } else {
        // Treat as search query
        url = `https://duckduckgo.com/?q=${encodeURIComponent(url)}`;
      }
    }

    if (window.phantomAPI) {
      window.phantomAPI.createTab(url);
    }
    
    // Update URL bar
    const urlBar = document.getElementById('url-bar') as HTMLInputElement;
    if (urlBar) {
      urlBar.value = url;
    }
    
    this.hideStartPage();
  }

  private performSearch(query: string): void {
    if (!query.trim()) return;
    
    const searchUrl = `https://duckduckgo.com/?q=${encodeURIComponent(query)}`;
    this.navigateToUrl(searchUrl);
  }

  private async createNewTab(url: string = 'about:blank'): Promise<void> {
    try {
      if (window.phantomAPI) {
        await window.phantomAPI.createTab(url);
        this.hideStartPage();
      }
    } catch (error) {
      console.error('Failed to create new tab:', error);
    }
  }

  private goBack(): void {
    window.history.back();
  }

  private goForward(): void {
    window.history.forward();
  }

  private refresh(): void {
    window.location.reload();
  }

  private goHome(): void {
    this.showStartPage();
  }

  private toggleTor(): void {
    const toggleBtn = document.getElementById('toggle-tor');
    if (toggleBtn) {
      const isEnabled = toggleBtn.textContent === 'Disable Tor';
      toggleBtn.textContent = isEnabled ? 'Enable Tor' : 'Disable Tor';
      
      // Update privacy status
      if (this.privacyStatus) {
        this.privacyStatus.torEnabled = !isEnabled;
        this.updatePrivacyIndicators();
      }
    }
  }

  private showSecurityDashboard(): void {
    alert('Security Dashboard - Feature coming soon!');
  }

  private showPrivacyCenter(): void {
    alert('Privacy Center - Feature coming soon!');
  }

  private showDownloads(): void {
    alert('Downloads - Feature coming soon!');
  }

  private showBookmarks(): void {
    alert('Bookmarks - Feature coming soon!');
  }

  private showSettings(): void {
    this.showModal('settings-modal');
    this.loadSettingsContent();
  }

  private showMenu(): void {
    alert('Menu - Feature coming soon!');
  }

  private showModal(modalId: string): void {
    const modal = document.getElementById(modalId);
    if (modal) {
      modal.classList.remove('hidden');
    }
  }

  private hideModal(modalId: string): void {
    const modal = document.getElementById(modalId);
    if (modal) {
      modal.classList.add('hidden');
    }
  }

  private showContextMenu(x: number, y: number): void {
    const contextMenu = document.getElementById('context-menu');
    if (contextMenu) {
      contextMenu.style.left = `${x}px`;
      contextMenu.style.top = `${y}px`;
      contextMenu.classList.remove('hidden');
    }
  }

  private hideContextMenu(): void {
    const contextMenu = document.getElementById('context-menu');
    if (contextMenu) {
      contextMenu.classList.add('hidden');
    }
  }

  private showStartPage(): void {
    const startPage = document.getElementById('start-page');
    if (startPage) {
      startPage.style.display = 'flex';
    }
  }

  private hideStartPage(): void {
    const startPage = document.getElementById('start-page');
    if (startPage) {
      startPage.style.display = 'none';
    }
  }

  private updateSecurityIndicator(): void {
    const indicator = document.getElementById('security-indicator');
    if (indicator && this.securityStatus) {
      if (this.securityStatus.httpsEnforced && this.securityStatus.sandboxingEnabled) {
        indicator.className = 'security-indicator secure';
        indicator.title = 'Secure Connection';
      } else {
        indicator.className = 'security-indicator warning';
        indicator.title = 'Connection Security Warning';
      }
    }
  }

  private updatePrivacyIndicators(): void {
    const torIndicator = document.getElementById('tor-indicator');
    const vpnIndicator = document.getElementById('vpn-indicator');
    const adblockIndicator = document.getElementById('adblock-indicator');

    if (this.privacyStatus) {
      if (torIndicator) {
        torIndicator.className = `privacy-indicator tor ${this.privacyStatus.torEnabled ? 'active' : ''}`;
        torIndicator.title = `Tor: ${this.privacyStatus.torEnabled ? 'Enabled' : 'Disabled'}`;
      }

      if (vpnIndicator) {
        vpnIndicator.className = `privacy-indicator vpn ${this.privacyStatus.vpnEnabled ? 'active' : ''}`;
        vpnIndicator.title = `VPN: ${this.privacyStatus.vpnEnabled ? 'Enabled' : 'Disabled'}`;
      }

      if (adblockIndicator) {
        adblockIndicator.className = `privacy-indicator adblock ${this.privacyStatus.adBlockingEnabled ? 'active' : ''}`;
        adblockIndicator.title = `Ad Blocker: ${this.privacyStatus.adBlockingEnabled ? 'Enabled' : 'Disabled'}`;
      }
    }
  }

  private updateStats(): void {
    const blockedCount = document.getElementById('blocked-count');
    const adsBlocked = document.getElementById('ads-blocked');
    const dataSaved = document.getElementById('data-saved');

    // Mock data for demonstration
    if (blockedCount) blockedCount.textContent = '1,247';
    if (adsBlocked) adsBlocked.textContent = '892';
    if (dataSaved) dataSaved.textContent = '15.3 MB';
  }

  private loadSettingsContent(): void {
    const settingsContent = document.getElementById('settings-content');
    if (settingsContent) {
      settingsContent.innerHTML = `
        <div class="settings-section">
          <h3>General Settings</h3>
          <div class="setting-item">
            <label>Default Search Engine</label>
            <select>
              <option value="duckduckgo">DuckDuckGo</option>
              <option value="startpage">Startpage</option>
              <option value="searx">SearX</option>
            </select>
          </div>
          <div class="setting-item">
            <label>
              <input type="checkbox" checked> Block third-party cookies
            </label>
          </div>
          <div class="setting-item">
            <label>
              <input type="checkbox" checked> Enable HTTPS everywhere
            </label>
          </div>
        </div>
      `;
    }
  }

  private showSettingsTab(tabName: string): void {
    // Update active tab
    document.querySelectorAll('.settings-tab').forEach(tab => {
      tab.classList.remove('active');
    });
    
    const activeTab = document.querySelector(`[data-tab="${tabName}"]`);
    if (activeTab) {
      activeTab.classList.add('active');
    }

    // Load tab content
    this.loadSettingsTabContent(tabName);
  }

  private loadSettingsTabContent(tabName: string): void {
    const settingsContent = document.getElementById('settings-content');
    if (!settingsContent) return;

    switch (tabName) {
      case 'general':
        this.loadSettingsContent();
        break;
      case 'security':
        settingsContent.innerHTML = '<div>Security settings content...</div>';
        break;
      case 'privacy':
        settingsContent.innerHTML = '<div>Privacy settings content...</div>';
        break;
      case 'advanced':
        settingsContent.innerHTML = '<div>Advanced settings content...</div>';
        break;
    }
  }
}

// Initialize the browser UI when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new PhantomBrowserUI();
});
