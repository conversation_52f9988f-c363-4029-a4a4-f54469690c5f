{"version": 3, "file": "dotnet.js", "sourceRoot": "", "sources": ["../../src/dotnet.ts"], "names": [], "mappings": ";;;AACA,uCAA2E;AAE3E;;;GAGG;AACH,SAAgB,mCAAmC;IACjD,QAAQ,OAAO,CAAC,QAAQ,EAAE;QACxB,0BAA0B;QAC1B,KAAK,OAAO;YACV,OAAO,sBAAsB,CAAC;QAChC,KAAK,QAAQ;YACX,OAAO,gEAAgE,CAAC;QAC1E,KAAK,OAAO;YACV,OAAO,qFAAqF,CAAC;QAC/F,0BAA0B;QAC1B;YACE,OAAO,mFAAmF,CAAC;KAC9F;AACH,CAAC;AAbD,kFAaC;AAED;;;;;;;;GAQG;AACH,SAAgB,sBAAsB,CAAC,gBAAyB;IAC9D,IAAI,gBAAgB,EAAE;QACpB,OAAO,gBAAgB,CAAC;KACzB;IAED,IAAI,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE;QAC3B,OAAO,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC;KAChC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAVD,wDAUC;AAED;;;GAGG;AACI,KAAK,UAAU,WAAW,CAC/B,GAAW,EACX,IAAqB,EACrB,OAA8B;;IAE9B,OAAO,aAAP,OAAO,cAAP,OAAO,IAAP,OAAO,GAAK,EAAE,EAAC;IACf,MAAA,OAAO,CAAC,mBAAmB,oCAA3B,OAAO,CAAC,mBAAmB,GAAK,mCAAmC,EAAE,EAAC;IACtE,OAAO,kCAAwB,CAAC,sBAAsB,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AAC9E,CAAC;AARD,kCAQC"}