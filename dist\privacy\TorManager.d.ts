interface TorConfig {
    socksPort: number;
    controlPort: number;
    dataDirectory: string;
    exitNodes?: string[];
    entryNodes?: string[];
    excludeNodes?: string[];
    bridges?: string[];
    useBridges: boolean;
}
interface TorStatus {
    isRunning: boolean;
    isConnected: boolean;
    circuitEstablished: boolean;
    currentIP?: string;
    entryNode?: string;
    exitNode?: string;
    bytesRead: number;
    bytesWritten: number;
}
export declare class TorManager {
    private torProcess;
    private config;
    private status;
    private controlSocket;
    private isStarting;
    constructor();
    start(): Promise<boolean>;
    stop(): Promise<void>;
    newIdentity(): Promise<boolean>;
    getStatus(): TorStatus;
    getCurrentIP(): Promise<string | null>;
    getCircuitInfo(): Promise<any>;
    updateConfig(newConfig: Partial<TorConfig>): void;
    private createTorConfig;
    private startTorProcess;
    private waitForTorReady;
    private testSocksConnection;
    private connectToControl;
    private authenticateControl;
    private sendControlCommand;
    private parseCircuitInfo;
    private getTorExecutablePath;
    private ensureDataDirectory;
}
export {};
//# sourceMappingURL=TorManager.d.ts.map