import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';

// Define the API interface for type safety
interface PhantomAPI {
  // Tab management
  createTab: (url: string) => Promise<string>;
  
  // Security
  getSecurityStatus: () => Promise<any>;
  
  // Privacy
  getPrivacyStatus: () => Promise<any>;
}

// Implement the secure API
const phantomAPI: PhantomAPI = {
  // Tab management
  createTab: (url: string) => ipcRenderer.invoke('create-tab', url),
  
  // Security
  getSecurityStatus: () => ipcRenderer.invoke('get-security-status'),
  
  // Privacy
  getPrivacyStatus: () => ipcRenderer.invoke('get-privacy-status')
};

// Security: Only expose the API we want to expose
contextBridge.exposeInMainWorld('phantomAPI', phantomAPI);

// Security: Block access to Node.js APIs
delete (window as any).require;
delete (window as any).exports;
delete (window as any).module;

// Security: Override dangerous globals
(window as any).eval = undefined;
(window as any).Function = undefined;

// Privacy: Override fingerprinting APIs
const originalGetContext = HTMLCanvasElement.prototype.getContext;
HTMLCanvasElement.prototype.getContext = function(contextType: string, ...args: any[]) {
  if (contextType === '2d' || contextType === 'webgl' || contextType === 'webgl2') {
    // Return a proxy that modifies fingerprinting methods
    const context = originalGetContext.call(this, contextType, ...args);
    if (context) {
      return createCanvasProxy(context);
    }
  }
  return originalGetContext.call(this, contextType, ...args);
};

function createCanvasProxy(context: any): any {
  return new Proxy(context, {
    get(target, prop) {
      if (prop === 'getImageData') {
        return function(...args: any[]) {
          const result = target[prop].apply(target, args);
          // Add noise to prevent fingerprinting
          if (result && result.data) {
            for (let i = 0; i < result.data.length; i += 4) {
              result.data[i] += Math.random() * 0.1 - 0.05; // Red
              result.data[i + 1] += Math.random() * 0.1 - 0.05; // Green
              result.data[i + 2] += Math.random() * 0.1 - 0.05; // Blue
            }
          }
          return result;
        };
      }
      
      if (prop === 'toDataURL') {
        return function(...args: any[]) {
          // Add slight noise to canvas data
          const imageData = target.getImageData(0, 0, target.canvas.width, target.canvas.height);
          for (let i = 0; i < imageData.data.length; i += 4) {
            imageData.data[i] += Math.random() * 0.1 - 0.05;
            imageData.data[i + 1] += Math.random() * 0.1 - 0.05;
            imageData.data[i + 2] += Math.random() * 0.1 - 0.05;
          }
          target.putImageData(imageData, 0, 0);
          return target[prop].apply(target, args);
        };
      }
      
      return target[prop];
    }
  });
}

// Privacy: Override WebGL fingerprinting
const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
WebGLRenderingContext.prototype.getParameter = function(parameter: number) {
  // Spoof common fingerprinting parameters
  switch (parameter) {
    case this.VENDOR:
      return 'Intel Inc.';
    case this.RENDERER:
      return 'Intel Iris OpenGL Engine';
    case this.VERSION:
      return 'WebGL 1.0 (OpenGL ES 2.0 Chromium)';
    case this.SHADING_LANGUAGE_VERSION:
      return 'WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)';
    default:
      return originalGetParameter.call(this, parameter);
  }
};

// Privacy: Override audio fingerprinting
const AudioContext = window.AudioContext || (window as any).webkitAudioContext;
if (AudioContext) {
  const originalCreateAnalyser = AudioContext.prototype.createAnalyser;
  AudioContext.prototype.createAnalyser = function() {
    const analyser = originalCreateAnalyser.call(this);
    const originalGetFloatFrequencyData = analyser.getFloatFrequencyData;
    analyser.getFloatFrequencyData = function(array: Float32Array) {
      originalGetFloatFrequencyData.call(this, array);
      // Add noise to prevent audio fingerprinting
      for (let i = 0; i < array.length; i++) {
        array[i] += Math.random() * 0.001 - 0.0005;
      }
    };
    return analyser;
  };
}

// Privacy: Override font enumeration
const originalFonts = (document as any).fonts;
if (originalFonts) {
  Object.defineProperty(document, 'fonts', {
    get: function() {
      // Return a limited set of fonts to prevent fingerprinting
      return {
        check: () => false,
        load: () => Promise.resolve([]),
        ready: Promise.resolve(),
        addEventListener: () => {},
        removeEventListener: () => {},
        values: () => [],
        entries: () => [],
        keys: () => [],
        forEach: () => {},
        has: () => false,
        add: () => {},
        delete: () => false,
        clear: () => {},
        size: 0
      };
    }
  });
}

// Security: CSP enforcement
const meta = document.createElement('meta');
meta.httpEquiv = 'Content-Security-Policy';
meta.content = "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https:; font-src 'self' data:; object-src 'none'; media-src 'self' https:; frame-src 'none';";
document.head.appendChild(meta);

console.log('Phantom Browser preload script loaded with security and privacy protections');
