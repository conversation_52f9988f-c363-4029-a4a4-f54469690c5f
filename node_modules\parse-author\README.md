# parse-author [![NPM version](https://img.shields.io/npm/v/parse-author.svg?style=flat)](https://www.npmjs.com/package/parse-author) [![NPM monthly downloads](https://img.shields.io/npm/dm/parse-author.svg?style=flat)](https://npmjs.org/package/parse-author)  [![NPM total downloads](https://img.shields.io/npm/dt/parse-author.svg?style=flat)](https://npmjs.org/package/parse-author) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/parse-author.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/parse-author)

> Parse a string into an object with `name`, `email` and `url` properties following npm conventions. Useful for the `authors` property in package.json or for parsing an AUTHORS file into an array of authors objects.

## Install

Install with [npm](https://www.npmjs.com/):

```sh
$ npm install --save parse-author
```

## Usage

```js
var parse = require('parse-author');
```

## Supported formats

Works with a flexible range of formats, any of the properties can be used or missing:

```
Name
Name <email> (url)
Name <email>(url)
Name<email> (url)
Name<email>(url)
Name (url) <email>
Name (url)<email>
Name(url) <email>
Name(url)<email>
Name (url)
Name(url)
Name <email>
Name<email>
<email> (url)
<email>(url)
(url) <email>
(url)<email>
<email>
(url)
```

## Examples

```js
var author = parse('Jon Schlinkert <<EMAIL>> (https://github.com/jonschlinkert)');
console.log(author);
//=> {name: 'Jon Schlinkert', email: '<EMAIL>', url: 'https://github.com/jonschlinkert'}

console.log(parse('Jon Schlinkert (https://github.com/jonschlinkert)'));
//=> {name: 'Jon Schlinkert', url: 'https://github.com/jonschlinkert'}

console.log(parse('Jon Schlinkert <<EMAIL>>'));
//=> {name: 'Jon Schlinkert', email: '<EMAIL>'}

console.log(parse(''));
//=> {}
```

## About

### Related projects

* [author-regex](https://www.npmjs.com/package/author-regex): Regular expression for parsing an `author` string into an object following npm conventions. | [homepage](https://github.com/jonschlinkert/author-regex "Regular expression for parsing an `author` string into an object following npm conventions.")
* [parse-authors](https://www.npmjs.com/package/parse-authors): Parse a string into an array of objects with `name`, `email` and `url` properties following… [more](https://github.com/jonschlinkert/parse-authors) | [homepage](https://github.com/jonschlinkert/parse-authors "Parse a string into an array of objects with `name`, `email` and `url` properties following npm conventions. Useful for the `authors` property in package.json or for parsing an AUTHORS file into an array of authors objects.")
* [stringify-author](https://www.npmjs.com/package/stringify-author): Stringify an authors object to `name <email> (url)`. | [homepage](https://github.com/jonschlinkert/stringify-author "Stringify an authors object to `name <email> (url)`.")
* [stringify-authors](https://www.npmjs.com/package/stringify-authors): Converts an author object or array of author objects into an array of strings. Useful… [more](https://github.com/jonschlinkert/stringify-authors) | [homepage](https://github.com/jonschlinkert/stringify-authors "Converts an author object or array of author objects into an array of strings. Useful for adding authors, maintainers or contributors to documentation, package.json or a readme.")

### Contributing

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).

### Contributors

| **Commits** | **Contributor** | 
| --- | --- |
| 14 | [slang800](https://github.com/slang800) |
| 12 | [jonschlinkert](https://github.com/jonschlinkert) |
| 1 | [MitMaro](https://github.com/MitMaro) |

### Building docs

_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_

To generate the readme, run the following command:

```sh
$ npm install -g verbose/verb#dev verb-generate-readme && verb
```

### Running tests

Running and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:

```sh
$ npm install && npm test
```

### Author

**Jon Schlinkert**

* [github/jonschlinkert](https://github.com/jonschlinkert)
* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)

### License

Copyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).
Released under the [MIT License](LICENSE).

***

_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.4.3, on March 08, 2017._