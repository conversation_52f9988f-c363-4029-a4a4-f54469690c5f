export declare class Store {
    private filePath;
    private encryptionKey;
    private data;
    constructor(fileName: string);
    set(key: string, value: any): void;
    get(key: string): any;
    has(key: string): boolean;
    delete(key: string): boolean;
    clear(): void;
    keys(): IterableIterator<string>;
    values(): IterableIterator<any>;
    entries(): IterableIterator<[string, any]>;
    size(): number;
    private deriveEncryptionKey;
    private getMachineId;
    private encrypt;
    private decrypt;
    private loadData;
    private saveData;
    backup(backupPath: string): boolean;
    restore(backupPath: string): boolean;
    secureDelete(): void;
    getStorePath(): string;
    getStoreSize(): number;
    isEncrypted(): boolean;
    getEncryptionInfo(): any;
}
//# sourceMappingURL=SecureStore.d.ts.map