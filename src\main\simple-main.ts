import { app, BrowserWindow, session, ipcMain, Menu } from 'electron';
import * as path from 'path';

class PhantomBrowser {
  private mainWindow: BrowserWindow | null = null;

  constructor() {
    this.initializeApp();
  }

  private initializeApp(): void {
    // Security: Disable node integration in renderer processes by default
    app.commandLine.appendSwitch('--disable-web-security', 'false');
    app.commandLine.appendSwitch('--site-per-process');
    app.commandLine.appendSwitch('--disable-features', 'VizDisplayCompositor');
    
    // Privacy: Disable telemetry and data collection
    app.commandLine.appendSwitch('--disable-background-timer-throttling');
    app.commandLine.appendSwitch('--disable-backgrounding-occluded-windows');
    app.commandLine.appendSwitch('--disable-renderer-backgrounding');
    
    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    app.whenReady().then(() => {
      this.setupSecurity();
      this.createMainWindow();
      this.setupMenu();
      this.setupIpcHandlers();
    });

    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });

    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        this.createMainWindow();
      }
    });

    // Security: Prevent new window creation from renderer
    app.on('web-contents-created', (event, contents) => {
      contents.on('will-navigate', (event, navigationUrl) => {
        const parsedUrl = new URL(navigationUrl);
        
        // Block dangerous protocols
        if (!['http:', 'https:', 'file:'].includes(parsedUrl.protocol)) {
          event.preventDefault();
        }
      });
    });
  }

  private setupSecurity(): void {
    // Configure session security
    const ses = session.defaultSession;
    
    // Security headers
    ses.webRequest.onHeadersReceived((details, callback) => {
      const responseHeaders = details.responseHeaders || {};
      
      // Add security headers
      responseHeaders['X-Frame-Options'] = ['DENY'];
      responseHeaders['X-Content-Type-Options'] = ['nosniff'];
      responseHeaders['X-XSS-Protection'] = ['1; mode=block'];
      responseHeaders['Referrer-Policy'] = ['strict-origin-when-cross-origin'];
      responseHeaders['Permissions-Policy'] = ['geolocation=(), microphone=(), camera=()'];
      
      callback({ responseHeaders });
    });

    // Clear data on startup for privacy
    ses.clearStorageData({
      storages: ['cookies', 'filesystem', 'indexdb', 'localstorage', 'shadercache', 'websql', 'serviceworkers', 'cachestorage']
    });

    // Configure privacy settings
    ses.setPermissionRequestHandler((webContents, permission, callback) => {
      // Deny all permissions by default for privacy
      callback(false);
    });

    // Block tracking and ads
    ses.webRequest.onBeforeRequest((details, callback) => {
      const url = new URL(details.url);
      
      // Block known tracking domains
      const trackingDomains = [
        'google-analytics.com',
        'googletagmanager.com',
        'facebook.com/tr',
        'doubleclick.net',
        'googlesyndication.com'
      ];

      if (trackingDomains.some(domain => url.hostname.includes(domain))) {
        callback({ cancel: true });
        return;
      }

      callback({ cancel: false });
    });
  }

  private createMainWindow(): void {
    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      show: false,
      icon: path.join(__dirname, '../../assets/icon.png'),
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, 'preload.js'),
        sandbox: true,
        webSecurity: true,
        allowRunningInsecureContent: false,
        experimentalFeatures: false
      }
    });

    this.mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));

    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow?.show();
      
      if (process.env.NODE_ENV === 'development') {
        this.mainWindow?.webContents.openDevTools();
      }
    });

    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });

    // Security: Prevent navigation to external sites in main window
    this.mainWindow.webContents.on('will-navigate', (event, url) => {
      if (!url.startsWith('file://')) {
        event.preventDefault();
      }
    });
  }

  private setupMenu(): void {
    const template: Electron.MenuItemConstructorOptions[] = [
      {
        label: 'File',
        submenu: [
          {
            label: 'New Tab',
            accelerator: 'CmdOrCtrl+T',
            click: () => this.createNewTab()
          },
          {
            label: 'New Private Window',
            accelerator: 'CmdOrCtrl+Shift+N',
            click: () => this.createPrivateWindow()
          },
          { type: 'separator' },
          {
            label: 'Exit',
            accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
            click: () => app.quit()
          }
        ]
      },
      {
        label: 'Security',
        submenu: [
          {
            label: 'Security Dashboard',
            click: () => this.showSecurityDashboard()
          },
          {
            label: 'Certificate Manager',
            click: () => this.showCertificateManager()
          }
        ]
      },
      {
        label: 'Privacy',
        submenu: [
          {
            label: 'Privacy Settings',
            click: () => this.showPrivacySettings()
          },
          {
            label: 'Clear Browsing Data',
            accelerator: 'CmdOrCtrl+Shift+Delete',
            click: () => this.clearBrowsingData()
          }
        ]
      }
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
  }

  private setupIpcHandlers(): void {
    ipcMain.handle('create-tab', (event, url: string) => {
      return this.createNewTab(url);
    });

    ipcMain.handle('get-security-status', () => {
      return {
        httpsEnforced: true,
        certificatePinningEnabled: false,
        fingerprintingProtectionEnabled: true,
        memoryIsolationEnabled: true,
        sandboxingEnabled: true,
        lastSecurityCheck: new Date()
      };
    });

    ipcMain.handle('get-privacy-status', () => {
      return {
        torEnabled: false,
        vpnEnabled: false,
        adBlockingEnabled: true,
        trackingProtectionEnabled: true,
        dnsOverHttpsEnabled: true,
        cookieBlockingEnabled: true,
        lastPrivacyCheck: new Date()
      };
    });
  }

  private createNewTab(url: string = 'about:blank'): string {
    // For now, just navigate the main window
    if (this.mainWindow && url !== 'about:blank') {
      this.mainWindow.loadURL(url);
    }
    return 'main-tab';
  }

  private createPrivateWindow(): void {
    const privateWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      show: false,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        sandbox: true,
        partition: 'private-session'
      }
    });

    privateWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
    privateWindow.once('ready-to-show', () => {
      privateWindow.show();
    });
  }

  private showSecurityDashboard(): void {
    console.log('Security Dashboard');
  }

  private showCertificateManager(): void {
    console.log('Certificate Manager');
  }

  private showPrivacySettings(): void {
    console.log('Privacy Settings');
  }

  private clearBrowsingData(): void {
    const ses = session.defaultSession;
    ses.clearStorageData();
    console.log('Browsing data cleared');
  }
}

// Initialize the browser
new PhantomBrowser();
